import { Ch<PERSON><PERSON>D<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mail, Instagram } from "lucide-react";
import { Button } from "@/components/ui/button";

const Hero = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section
      id="home"
      className="min-h-screen flex items-center justify-center relative pt-20"
    >
      {/* 3D Geometric Shape */}
      <div className="absolute top-1/2 right-1/4 transform -translate-y-1/2 w-96 h-96 opacity-30">
        <div className="relative w-full h-full animate-spin-slow">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500 to-purple-600 transform rotate-12 rounded-lg shadow-2xl"></div>
          <div className="absolute top-4 left-4 right-4 bottom-4 bg-gradient-to-br from-cyan-500 to-blue-600 transform -rotate-12 rounded-lg shadow-2xl"></div>
          <div className="absolute top-8 left-8 right-8 bottom-8 bg-gradient-to-br from-purple-500 to-pink-600 transform rotate-6 rounded-lg shadow-2xl"></div>
        </div>
        <div className="absolute top-1/3 right-0 w-16 h-16 bg-orange-400 rounded-full animate-pulse"></div>
      </div>

      <div className="container mx-auto px-6 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-6xl lg:text-8xl font-bold mb-6 bg-gradient-to-r from-white via-cyan-200 to-white bg-clip-text text-transparent animate-fade-in">
            TANISHQ
          </h1>
          <p className="text-xl lg:text-2xl text-slate-300 mb-8 animate-fade-in animation-delay-200">
            SOFTWARE ENGINEER, FULL STACK & BLOCKCHAIN DEVELOPER.
          </p>

          {/* Featured In Section */}
          <div className="mb-12 animate-fade-in animation-delay-400">
            <p className="text-slate-400 text-sm mb-6 uppercase tracking-wide">
              EXPERTISE IN
            </p>
            <div className="flex justify-center items-center space-x-8 opacity-60">
              <div className="text-slate-400 text-sm">React.js</div>
              <div className="text-slate-400 text-sm">Node.js</div>
              <div className="text-slate-400 text-sm">Blockchain</div>
              <div className="text-slate-400 text-sm">AI/ML</div>
              <div className="text-slate-400 text-sm">Full Stack</div>
            </div>
          </div>

          {/* Social Links */}
          <div className="flex justify-center space-x-6 mb-12 animate-fade-in animation-delay-600">
            <Button
              variant="ghost"
              size="icon"
              className="text-slate-300 hover:text-cyan-400 transition-colors"
              asChild
            >
              <a
                href="https://github.com/RsTanishq"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Github size={20} />
              </a>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-slate-300 hover:text-cyan-400 transition-colors"
              asChild
            >
              <a
                href="https://www.linkedin.com/in/tanishqbhartwal777"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Linkedin size={20} />
              </a>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-slate-300 hover:text-cyan-400 transition-colors"
              asChild
            >
              <a href="mailto:<EMAIL>">
                <Mail size={20} />
              </a>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-slate-300 hover:text-cyan-400 transition-colors"
              asChild
            >
              <a
                href="https://www.instagram.com/tanishqb_7?igsh=ZHN6Mzk5aDZ0enBz"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Instagram size={20} />
              </a>
            </Button>
          </div>

          {/* Scroll Indicator */}
          <button
            onClick={() => scrollToSection("expertise")}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-slate-400 hover:text-cyan-400 transition-colors animate-bounce"
          >
            <ChevronDown size={24} />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
