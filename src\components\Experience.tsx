
import { MapPin, ExternalLink, Calendar, GraduationCap, Award } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const Experience = () => {
  const experiences = [
    {
      title: "B.tech(CSE) @ Galgotias College of Engineering",
      period: "2021 - 2025",
      location: "Gr.Noida, UP",
      description: "Currently pursuing Computer Science Engineering with a CGPA of 7. Focused on Data Structures & Algorithms, Network Security, Database Management Systems, AI, Web Development, and Cyber Security.",
      tags: ["Python", "Java", "C", "SQL", "React.js", "AI/ML"],
      logo: "🎓",
      expanded: true,
      type: "education"
    },
    {
      title: "Class XII - Delhi Public School Raj Nagar",
      period: "2021",
      location: "Ghaziabad, UP",
      description: "Completed Higher Secondary Education (PCM) with 76% marks.",
      tags: ["Physics", "Chemistry", "Mathematics"],
      logo: "📚",
      type: "education"
    },
    {
      title: "Class X - Delhi Public School Raj Nagar", 
      period: "2019",
      location: "Ghaziabad, UP",
      description: "Completed Secondary Education with 80% marks.",
      tags: ["All Subjects"],
      logo: "📖",
      type: "education"
    }
  ];

  const achievements = [
    {
      title: "LeetCode Problem Solver",
      description: "Solved 400+ problems on LeetCode, demonstrating strong problem-solving skills and algorithmic thinking.",
      icon: "🏆"
    },
    {
      title: "Full-Stack Web Development Bootcamp",
      description: "Completed comprehensive bootcamp from Udemy in 2024, covering modern web development technologies.",
      icon: "🎯"
    }
  ];

  return (
    <section id="experience" className="py-20 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent">
            Education & Achievements
          </h2>
        </div>

        {/* Education Timeline */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-white mb-8 flex items-center gap-3">
            <GraduationCap className="text-cyan-400" size={28} />
            Education
          </h3>
          <div className="space-y-6">
            {experiences.map((exp, index) => (
              <Card key={index} className={`bg-gradient-to-r ${
                exp.expanded 
                  ? "from-purple-500/20 to-blue-500/20 border-purple-500/50" 
                  : "from-slate-800 to-slate-900 border-slate-700"
              } p-6 hover:scale-[1.02] transition-all duration-300`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">{exp.logo}</div>
                    <div>
                      <h4 className="text-xl font-bold text-white">{exp.title}</h4>
                      <div className="flex items-center space-x-4 text-slate-400 text-sm">
                        <div className="flex items-center space-x-1">
                          <MapPin size={14} />
                          <span>{exp.location}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-semibold">{exp.period}</div>
                  </div>
                </div>

                {exp.expanded && (
                  <div className="space-y-4">
                    <p className="text-slate-300 leading-relaxed">{exp.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {exp.tags.map((tag) => (
                        <span key={tag} className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm border border-blue-500/30">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {!exp.expanded && (
                  <div className="flex flex-wrap gap-2">
                    {exp.tags.map((tag) => (
                      <span key={tag} className="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </Card>
            ))}
          </div>
        </div>

        {/* Achievements & Certifications */}
        <div>
          <h3 className="text-2xl font-bold text-white mb-8 flex items-center gap-3">
            <Award className="text-cyan-400" size={28} />
            Achievements & Certifications
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            {achievements.map((achievement, index) => (
              <Card key={index} className="bg-gradient-to-r from-slate-800 to-slate-900 border-slate-700 p-6 hover:scale-105 transition-transform duration-300">
                <div className="flex items-start space-x-4">
                  <div className="text-3xl">{achievement.icon}</div>
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">{achievement.title}</h4>
                    <p className="text-slate-300 text-sm">{achievement.description}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Technical Skills Summary */}
        <div className="mt-16 bg-slate-800/50 rounded-lg p-6 border border-slate-700">
          <div className="text-cyan-400 text-sm font-mono">
            <div className="text-slate-500">// Technical Skills Overview</div>
            <div className="mt-4 space-y-2">
              <div><span className="text-purple-400">Programming Languages:</span> <span className="text-orange-400">Python, Java, C, SQL</span></div>
              <div><span className="text-purple-400">Web Development:</span> <span className="text-orange-400">HTML, CSS, JavaScript, React.js, Tailwind CSS, Node.js, Express.js</span></div>
              <div><span className="text-purple-400">Developer Tools:</span> <span className="text-orange-400">VS Code, Android Studio, Replit</span></div>
              <div><span className="text-purple-400">Technologies & Frameworks:</span> <span className="text-orange-400">Git, GitHub, DBMS, AI, OOPs, OS, Network Security</span></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Experience;
