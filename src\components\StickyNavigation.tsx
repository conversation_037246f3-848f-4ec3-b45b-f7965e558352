import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface StickyNavigationProps {
  activeSection: string;
  isMenuOpen: boolean;
  setIsMenuOpen: (open: boolean) => void;
}

const StickyNavigation = ({
  activeSection,
  isMenuOpen,
  setIsMenuOpen,
}: StickyNavigationProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const navItems = [
    { id: "home", label: "home", number: "01" },
    { id: "expertise", label: "expertise", number: "02" },
    { id: "work", label: "work", number: "03" },
    { id: "experience", label: "experience", number: "04" },
    { id: "contact", label: "contact", number: "05" },
  ];

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const shouldShow = scrollTop > 200;

      if (shouldShow && !isVisible) {
        setIsVisible(true);
      } else if (!shouldShow && isVisible) {
        setIsVisible(false);
      }

      setIsScrolled(scrollTop > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isVisible]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
    setIsMenuOpen(false);
  };

  return (
    <>
      {/* Sticky Navigation - Appears on Scroll */}
      <nav
        className={`fixed top-0 left-0 right-0 z-50 bg-slate-900/95 backdrop-blur-md border-b border-cyan-500/20 transition-all duration-500 ${
          isVisible
            ? "translate-y-0 opacity-100"
            : "-translate-y-full opacity-0"
        } ${isScrolled ? "shadow-lg shadow-cyan-500/10" : ""}`}
      >
        <div className="container mx-auto px-6 py-4">
          {/* Desktop Menu */}
          <div className="hidden lg:flex items-center justify-between">
            {/* Logo/Brand */}
            <div className="text-cyan-400 font-mono font-bold text-lg">
              RSTB
            </div>

            {/* Navigation Items */}
            <div className="flex items-center space-x-8">
              {navItems.map((item, index) => {
                const isActive = activeSection === item.id;

                return (
                  <button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    className={`font-mono text-sm transition-all duration-300 hover:text-cyan-400 hover:scale-105 relative ${
                      isActive
                        ? "text-cyan-400 font-bold scale-105"
                        : "text-slate-300"
                    } ${isVisible ? "animate-fade-in" : "opacity-0"}`}
                    style={{
                      animationDelay: isVisible ? `${index * 100}ms` : "0ms",
                    }}
                  >
                    {/* Active section glow effect */}
                    {isActive && (
                      <div className="absolute inset-0 bg-cyan-400/20 rounded-lg blur-sm animate-pulse -z-10"></div>
                    )}

                    <span
                      className={`relative z-10 mr-2 text-xs transition-colors duration-300 ${
                        isActive ? "text-cyan-300" : "text-slate-500"
                      }`}
                    >
                      {item.number}
                    </span>
                    <span
                      className={`relative z-10 mr-1 transition-colors duration-300 ${
                        isActive ? "text-cyan-300" : "text-slate-400"
                      }`}
                    >
                      //
                    </span>
                    <span
                      className={`relative z-10 transition-colors duration-300 ${
                        isActive ? "text-white font-semibold" : "text-white"
                      }`}
                    >
                      {item.label}
                    </span>

                    {/* Active section underline */}
                    {isActive && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-400 animate-fade-in"></div>
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="lg:hidden flex items-center justify-between">
            {/* Logo/Brand */}
            <div className="text-cyan-400 font-mono font-bold text-lg">
              RSTB
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className={`text-white hover:bg-slate-800 transition-all duration-300 ${
                isVisible ? "animate-fade-in" : "opacity-0"
              }`}
              style={{
                animationDelay: isVisible ? "300ms" : "0ms",
              }}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-slate-900/95 backdrop-blur-md border-t border-slate-700/50 animate-fade-in">
            <div className="container mx-auto px-6 py-4">
              <div className="flex flex-col space-y-4">
                {navItems.map((item, index) => {
                  const isActive = activeSection === item.id;

                  return (
                    <button
                      key={item.id}
                      onClick={() => scrollToSection(item.id)}
                      className={`text-left font-mono text-sm transition-all duration-300 hover:text-cyan-400 hover:translate-x-2 animate-fade-in relative ${
                        isActive
                          ? "text-cyan-400 font-bold scale-105"
                          : "text-slate-300"
                      }`}
                      style={{
                        animationDelay: `${index * 100}ms`,
                      }}
                    >
                      {/* Active section glow effect for mobile */}
                      {isActive && (
                        <div className="absolute inset-0 bg-cyan-400/10 rounded-lg blur-sm animate-pulse -z-10"></div>
                      )}

                      <span
                        className={`relative z-10 mr-2 text-xs transition-colors duration-300 ${
                          isActive ? "text-cyan-300" : "text-slate-500"
                        }`}
                      >
                        {item.number}
                      </span>
                      <span
                        className={`relative z-10 mr-1 transition-colors duration-300 ${
                          isActive ? "text-cyan-300" : "text-slate-400"
                        }`}
                      >
                        //
                      </span>
                      <span
                        className={`relative z-10 transition-colors duration-300 ${
                          isActive ? "text-white font-semibold" : "text-white"
                        }`}
                      >
                        {item.label}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </nav>
    </>
  );
};

export default StickyNavigation;
