import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface NavigationProps {
  activeSection: string;
  isMenuOpen: boolean;
  setIsMenuOpen: (open: boolean) => void;
}

const Navigation = ({
  activeSection,
  isMenuOpen,
  setIsMenuOpen,
}: NavigationProps) => {
  const [isVisible, setIsVisible] = useState(false);

  const navItems = [
    { id: "home", label: "home", number: "01" },
    { id: "expertise", label: "expertise", number: "02" },
    { id: "work", label: "work", number: "03" },
    { id: "experience", label: "experience", number: "04" },
    { id: "contact", label: "contact", number: "05" },
  ];

  useEffect(() => {
    // Fade-in animation when website loads
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
    setIsMenuOpen(false);
  };

  return (
    <>
      {/* Desktop Navigation - MOVES WITH SCROLL */}
      <nav
        className={`bg-slate-900/95 backdrop-blur-md transition-all duration-700 ${
          isVisible ? "opacity-100" : "opacity-0"
        }`}
      >
        <div className="container mx-auto px-6 py-6">
          {/* Centered Desktop Menu */}
          <div className="hidden lg:flex items-center justify-center">
            <div className="flex items-center space-x-12">
              {navItems.map((item, index) => (
                <button
                  key={item.id}
                  onClick={() => scrollToSection(item.id)}
                  className={`font-mono text-sm transition-all duration-500 hover:text-cyan-400 hover:scale-105 ${
                    activeSection === item.id
                      ? "text-cyan-400 font-bold"
                      : "text-slate-300"
                  } ${isVisible ? "animate-fade-in" : "opacity-0"}`}
                  style={{
                    animationDelay: isVisible ? `${index * 150}ms` : "0ms",
                  }}
                >
                  <span className="text-slate-500 mr-2 text-xs">
                    {item.number}
                  </span>
                  <span className="text-slate-400 mr-1">//</span>
                  <span className="text-white">{item.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden flex justify-end">
            <Button
              variant="ghost"
              size="icon"
              className={`text-white hover:bg-slate-800 transition-all duration-500 ${
                isVisible ? "animate-fade-in" : "opacity-0"
              }`}
              style={{
                animationDelay: isVisible ? "750ms" : "0ms",
              }}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden bg-slate-900/95 backdrop-blur-md border-t border-slate-700/50">
            <div className="container mx-auto px-6 py-4">
              <div className="flex flex-col space-y-4">
                {navItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    className={`text-left font-mono text-sm transition-colors duration-300 hover:text-cyan-400 ${
                      activeSection === item.id
                        ? "text-cyan-400"
                        : "text-slate-300"
                    }`}
                  >
                    <span className="text-slate-500 mr-2 text-xs">
                      {item.number}
                    </span>
                    <span className="text-slate-400 mr-1">//</span>
                    <span className="text-white">{item.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </nav>
    </>
  );
};

export default Navigation;
