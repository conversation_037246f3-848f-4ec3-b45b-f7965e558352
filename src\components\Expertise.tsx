import { Monitor, Atom, Smartphone, Blocks } from "lucide-react";
import { Card } from "@/components/ui/card";

const Expertise = () => {
  const expertiseAreas = [
    {
      icon: Monitor,
      title: "Software",
      subtitle: "Development",
      description:
        "Experienced in both functional and OOP: Python, C, Java, JavaScript, TypeScript.",
      accent: "text-purple-400",
      underlineColor: "chonky-underline-megenta",
    },
    {
      icon: Atom,
      title: "Frontend Dev",
      subtitle: "React, NextJS",
      description:
        "Passionate about UI/UX design with hands-on experience in HTML, CSS, JavaScript, and frameworks like React and Next.js through academic projects and personal work.",
      accent: "text-blue-400",
      underlineColor: "chonky-underline-blue",
    },
    {
      icon: Smartphone,
      title: "Flutter Dev",
      subtitle: "Android, iOS",
      description:
        "Skilled in developing hybrid mobile apps and cross-platform solutions using the Flutter framework.",
      accent: "text-yellow-400",
      underlineColor: "chonky-underline-yellow",
    },
    {
      icon: Blocks,
      title: "Blockchain Dev",
      subtitle: "Solidity, Web3",
      description:
        "Experienced in developing smart contracts and decentralized applications using Solidity and Web3 technologies.",
      accent: "text-green-400",
      underlineColor: "chonky-underline-green",
    },
  ];

  return (
    <section id="expertise" className="py-20 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-white">
            My Expertise
          </h2>
        </div>

        <div className="grid lg:grid-cols-2 xl:grid-cols-4 gap-8 max-w-7xl mx-auto">
          {expertiseAreas.map((area, index) => {
            const IconComponent = area.icon;
            return (
              <Card
                key={index}
                className={`bg-slate-900/50 border-slate-600 p-8 hover:border-slate-500 transition-all duration-300 animate-fade-in ${
                  index === 0
                    ? ""
                    : index === 1
                    ? "animation-delay-200"
                    : index === 2
                    ? "animation-delay-400"
                    : "animation-delay-600"
                }`}
              >
                <div className="flex items-start gap-4 mb-6">
                  <div className="flex-shrink-0">
                    <IconComponent size={32} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white mb-1">
                      <span className={`${area.accent} ${area.underlineColor}`}>
                        {area.title}
                      </span>
                    </h3>
                    <p className="text-white text-lg">{area.subtitle}</p>
                  </div>
                </div>

                <div className="font-mono text-sm">
                  <div className="text-slate-500 mb-2">&lt;h3&gt;</div>
                  <div className="text-slate-300 leading-relaxed mb-2 pl-4">
                    {area.description}
                  </div>
                  <div className="text-slate-500">&lt;/h3&gt;</div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Code snippet decoration */}
        <div className="mt-16 bg-slate-900/30 rounded-lg p-6 border border-slate-700/50 max-w-4xl mx-auto">
          <div className="text-sm font-mono">
            <div className="text-slate-500 mb-2">
              // Always learning new technologies and building amazing projects
            </div>
            <div className="text-slate-400">
              <span className="text-purple-400">const</span>{" "}
              <span className="text-cyan-400">mySkills</span> = {"{"}
            </div>
            <div className="ml-4 text-slate-300">
              <span className="text-orange-400">languages</span>: [
              <span className="text-green-400">'Python'</span>,{" "}
              <span className="text-green-400">'Java'</span>,{" "}
              <span className="text-green-400">'C'</span>,{" "}
              <span className="text-green-400">'JavaScript'</span>,{" "}
              <span className="text-green-400">'SQL'</span>],
            </div>
            <div className="ml-4 text-slate-300">
              <span className="text-orange-400">frontend</span>: [
              <span className="text-green-400">'HTML'</span>,{" "}
              <span className="text-green-400">'CSS'</span>,{" "}
              <span className="text-green-400">'React.js'</span>,{" "}
              <span className="text-green-400">'Tailwind CSS'</span>],
            </div>
            <div className="ml-4 text-slate-300">
              <span className="text-orange-400">backend</span>: [
              <span className="text-green-400">'Node.js'</span>,{" "}
              <span className="text-green-400">'Express.js'</span>],
            </div>
            <div className="ml-4 text-slate-300">
              <span className="text-orange-400">mobile</span>: [
              <span className="text-green-400">'Flutter'</span>,{" "}
              <span className="text-green-400">'React Native'</span>],
            </div>
            <div className="ml-4 text-slate-300">
              <span className="text-orange-400">blockchain</span>: [
              <span className="text-green-400">'Solidity'</span>,{" "}
              <span className="text-green-400">'Web3.js'</span>,{" "}
              <span className="text-green-400">'Ethereum'</span>],
            </div>
            <div className="ml-4 text-slate-300">
              <span className="text-orange-400">tools</span>: [
              <span className="text-green-400">'VS Code'</span>,{" "}
              <span className="text-green-400">'Android Studio'</span>,{" "}
              <span className="text-green-400">'Replit'</span>,{" "}
              <span className="text-green-400">'Git'</span>,{" "}
              <span className="text-green-400">'GitHub'</span>],
            </div>
            <div className="ml-4 text-slate-300">
              <span className="text-orange-400">technologies</span>: [
              <span className="text-green-400">'DBMS'</span>,{" "}
              <span className="text-green-400">'AI'</span>,{" "}
              <span className="text-green-400">'OOPs'</span>,{" "}
              <span className="text-green-400">'OS'</span>,{" "}
              <span className="text-green-400">'Network Security'</span>]
            </div>
            <div className="text-slate-400">{"}"}</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Expertise;
