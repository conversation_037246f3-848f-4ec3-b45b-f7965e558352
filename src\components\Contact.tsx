import {
  Mail,
  MessageSquare,
  <PERSON><PERSON><PERSON>,
  <PERSON>ed<PERSON>,
  Instagram,
  Send,
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Tit<PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import emailjs from "@emailjs/browser";
import { useState } from "react";
import { toast } from "sonner";
import { emailjsConfig } from "@/lib/emailjs-config";

// Form validation schema
const contactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

type ContactFormValues = z.infer<typeof contactFormSchema>;

const Contact = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      message: "",
    },
  });

  const onSubmit = async (values: ContactFormValues) => {
    setIsSubmitting(true);

    try {
      const templateParams = {
        from_name: values.name,
        from_email: values.email,
        message: values.message,
        to_name: "Tanishq",
      };

      await emailjs.send(
        emailjsConfig.serviceId,
        emailjsConfig.templateId,
        templateParams,
        emailjsConfig.publicKey
      );

      toast.success("Message sent successfully! I'll get back to you soon.");
      form.reset();
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error(
        "Failed to send message. Please try again or contact me directly."
      );
    } finally {
      setIsSubmitting(false);
    }
  };
  const testimonials = [
    {
      text: "Tanishq has demonstrated exceptional problem-solving skills by solving 400+ problems on LeetCode. His approach to algorithmic thinking and data structures is commendable.",
      author: "LeetCode Community",
      role: "Competitive Programming Platform",
      avatar: "🏆",
      gradient: "from-purple-500 to-pink-500",
    },
    {
      text: "Outstanding performance in the Full-Stack Web Development Bootcamp. Tanishq showed great dedication to learning modern web technologies and best practices.",
      author: "Udemy Instructor",
      role: "Full-Stack Development Course",
      avatar: "🎯",
      gradient: "from-cyan-500 to-blue-500",
    },
    {
      text: "Impressive blockchain project showcasing deep understanding of decentralized systems and smart contract development. Great attention to security and user experience.",
      author: "Tech Reviewer",
      role: "Blockchain Technology Expert",
      avatar: "⛓️",
      gradient: "from-purple-600 to-indigo-500",
    },
  ];

  return (
    <section id="contact" className="py-20 relative">
      <div className="container mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Info */}
          <div>
            <h2 className="text-4xl lg:text-5xl font-bold mb-8 bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent">
              Let's Connect & Build Something Amazing
            </h2>

            <div className="space-y-6 mb-12">
              <p className="text-lg text-slate-300 leading-relaxed">
                I'm actively seeking opportunities to collaborate on innovative
                projects and contribute to meaningful solutions.
              </p>
              <p className="text-lg text-slate-300 leading-relaxed">
                Whether it's full-stack development, blockchain solutions, or
                AI/ML projects, I'm excited to discuss how we can work together!
              </p>
            </div>

            <div className="space-y-4 mb-12">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-3 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer"
              >
                <Mail size={20} />
                <span className="text-lg underline"><EMAIL></span>
              </a>

              <div className="space-y-2 text-slate-300">
                <div className="flex items-center space-x-3">
                  <MessageSquare size={20} className="text-slate-400" />
                  <span>Available for freelance opportunities</span>
                </div>
                <a
                  href="https://www.linkedin.com/in/tanishqbhartwal777"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 hover:text-cyan-400 transition-colors cursor-pointer"
                >
                  <Linkedin size={20} className="text-slate-400" />
                  <span>Open to professional networking</span>
                </a>
                <a
                  href="https://www.instagram.com/tanishqb_7?igsh=ZHN6Mzk5aDZ0enBz"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 hover:text-cyan-400 transition-colors cursor-pointer"
                >
                  <Instagram size={20} className="text-slate-400" />
                  <span>Follow my tech journey</span>
                </a>
                <a
                  href="https://github.com/RsTanishq"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 hover:text-cyan-400 transition-colors cursor-pointer"
                >
                  <Github size={20} className="text-slate-400" />
                  <span>Check out my repositories</span>
                </a>
              </div>
            </div>

            {/* Say Hello Button */}
            <div className="mb-12">
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <Send className="mr-2 h-4 w-4" />
                    Say Hello
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px] bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700 shadow-2xl">
                  <DialogHeader className="text-center pb-6">
                    <DialogTitle className="text-2xl font-bold text-white mb-2">
                      Thanks for taking the time to reach out.
                    </DialogTitle>
                    <p className="text-slate-300 text-lg">
                      How can I help you today?
                    </p>
                  </DialogHeader>

                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="space-y-6"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-slate-300">
                                Name
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Your name"
                                  {...field}
                                  className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-200"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-slate-300">
                                Email
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="<EMAIL>"
                                  type="email"
                                  {...field}
                                  className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-200"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="message"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-slate-300">
                              Message
                            </FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Tell me about your project or how I can help you..."
                                className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 min-h-[120px] resize-none transition-all duration-200"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-center pt-4">
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                        >
                          {isSubmitting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Sending...
                            </>
                          ) : (
                            <>
                              <Send className="mr-2 h-4 w-4" />
                              Submit
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </DialogContent>
              </Dialog>
            </div>

            {/* Status indicator */}
            <div className="flex items-center space-x-2 text-slate-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse animation-delay-200"></div>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse animation-delay-400"></div>
              <span className="ml-2 text-green-400">
                Available for new opportunities
              </span>
            </div>
          </div>

          {/* Testimonials */}
          <div className="space-y-6">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className={`bg-gradient-to-br ${testimonial.gradient} p-6 text-white relative overflow-hidden`}
              >
                <div className="absolute top-4 left-4 text-4xl opacity-30">
                  "
                </div>
                <div className="absolute top-4 right-4 text-4xl opacity-30">
                  "
                </div>

                <div className="relative z-10">
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-xl">
                      {testimonial.avatar}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm leading-relaxed mb-4">
                        {testimonial.text}
                      </p>
                      <div>
                        <div className="font-semibold">
                          {testimonial.author}
                        </div>
                        <div className="text-sm opacity-90">
                          {testimonial.role}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
