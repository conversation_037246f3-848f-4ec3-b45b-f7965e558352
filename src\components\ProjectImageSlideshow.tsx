import React, { useEffect, useState, useRef } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { type CarouselApi } from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";

interface ProjectImageSlideshowProps {
  images: string[];
  alt: string;
  className?: string;
}

export const ProjectImageSlideshow: React.FC<ProjectImageSlideshowProps> = ({
  images,
  alt,
  className = "",
}) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const autoplayRef = useRef<any>(null);

  // Initialize autoplay plugin with proper typing
  const plugin = React.useMemo(
    () => Autoplay({ delay: 3000, stopOnInteraction: true }) as any,
    []
  );

  // Store the plugin reference
  useEffect(() => {
    autoplayRef.current = plugin;
  }, [plugin]);

  // Reset current when images change
  useEffect(() => {
    if (images && images.length > 0) {
      setCurrent(0);
    }
  }, [images]);

  // Setup carousel API listeners
  useEffect(() => {
    if (!api || !images || images.length === 0) {
      return;
    }

    const updateCurrent = () => {
      const selectedIndex = api.selectedScrollSnap();
      if (selectedIndex >= 0 && selectedIndex < images.length) {
        setCurrent(selectedIndex);
      }
    };

    // Set initial current index
    updateCurrent();

    // Listen for slide changes
    api.on("select", updateCurrent);

    return () => {
      api.off("select", updateCurrent);
    };
  }, [api, images]);

  // If no images provided, show a placeholder
  if (!images || images.length === 0) {
    return (
      <div
        className={`w-full h-full bg-gradient-to-br from-cyan-500/20 to-purple-500/20 rounded-lg flex items-center justify-center ${className}`}
      >
        <div className="text-4xl">📷</div>
      </div>
    );
  }

  // If only one image, show it without carousel
  if (images.length === 1) {
    return (
      <div
        className={`relative w-full h-full border border-slate-700/50 rounded-lg overflow-hidden bg-slate-900/20 ${className}`}
      >
        <div className="absolute inset-0">
          <img
            src={images[0]}
            alt={alt}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = "none";
              target.parentElement!.innerHTML =
                '<div class="w-full h-full bg-gradient-to-br from-cyan-500/20 to-purple-500/20 rounded-lg flex items-center justify-center"><div class="text-4xl">📷</div></div>';
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className={`relative w-full h-full border border-slate-700/50 rounded-lg overflow-hidden bg-slate-900/20 ${className}`}
    >
      {/* Fixed container with border */}
      <div className="absolute inset-0">
        <Carousel
          setApi={setApi}
          className="w-full h-full"
          plugins={[plugin]}
          opts={{
            align: "start",
            loop: true,
          }}
          onMouseEnter={() => autoplayRef.current?.stop()}
          onMouseLeave={() => autoplayRef.current?.reset()}
        >
          <CarouselContent className="h-full -ml-0">
            {images.map((image, index) => (
              <CarouselItem key={`${image}-${index}`} className="h-full pl-0">
                <div className="relative w-full h-full">
                  <img
                    src={image}
                    alt={`${alt} - Image ${index + 1}`}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                    style={{ display: "block" }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = "none";
                      target.parentElement!.innerHTML =
                        '<div class="w-full h-full bg-gradient-to-br from-cyan-500/20 to-purple-500/20 flex items-center justify-center"><div class="text-4xl">📷</div><div class="text-sm text-white mt-2">Image not found</div></div>';
                    }}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>

        {/* Dots indicator */}
        {images.length > 1 && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 z-20">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  index === current
                    ? "bg-cyan-400 scale-110"
                    : "bg-white/50 hover:bg-white/70"
                }`}
                onClick={() => {
                  if (api && index >= 0 && index < images.length) {
                    api.scrollTo(index);
                  }
                }}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
