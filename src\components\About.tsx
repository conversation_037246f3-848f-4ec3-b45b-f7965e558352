
import { Code, Smartphone, Database } from "lucide-react";
import { Card } from "@/components/ui/card";

const About = () => {
  return (
    <section id="about" className="py-20 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent">
            About Me
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            Computer Science Engineering student passionate about creating innovative solutions through blockchain, AI, and full-stack development.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div>
            <p className="text-lg text-slate-300 mb-6 leading-relaxed">
              I'm a Computer Science Engineering student at Galgotias College of Engineering with a strong foundation in 
              programming languages including Python, Java, C, and SQL. Currently pursuing my B.tech(CSE) with a CGPA of 7.
            </p>
            <p className="text-lg text-slate-300 mb-8 leading-relaxed">
              I specialize in full-stack web development, blockchain technology, and artificial intelligence. My projects range 
              from weather applications to complex healthcare management systems using blockchain and smart contracts.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400 mb-1">400+</div>
                <div className="text-slate-400 text-sm">LeetCode Problems</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400 mb-1">76%</div>
                <div className="text-slate-400 text-sm">Class XII Score</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400 mb-1">80%</div>
                <div className="text-slate-400 text-sm">Class X Score</div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 border border-slate-700">
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <Code size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">Web Development</h3>
                    <p className="text-slate-400 text-sm">HTML, CSS, JavaScript, React.js, Tailwind CSS</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <Smartphone size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">Blockchain Development</h3>
                    <p className="text-slate-400 text-sm">MERN Stack, Ethereum, Smart Contracts</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                    <Database size={24} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">Programming & AI</h3>
                    <p className="text-slate-400 text-sm">Python, Java, C, SQL, AI/ML, OOPs</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
